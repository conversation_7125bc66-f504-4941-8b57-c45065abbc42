/**
 * Webflow Button Integration System
 * Integra botões do template Webflow com o sistema de navegação
 * Now with Typebot integration support
 */
import { supabase, TABLE_NAME, validateSupabaseConfig } from '../config/supabase.js';
import { dgmCanvasIntegration } from './dgm-canvas-integration.js';

export class WebflowButtonSystem {
  constructor() {
    this.isInitialized = false;
    this.stepNavigationSystem = null;
    this.supabaseClient = null;
    this.debugMode = false;
    this.typebotIntegration = null; // Will be set by the main app
    this.useTypebot = true; // Flag to enable/disable Typebot integration
  }

  async init(stepNavigationSystem = null) {
    if (this.isInitialized) return;

    try {
      this.stepNavigationSystem = stepNavigationSystem;

      await this.waitForDOM();
      this.setupNextButtons();
      this.setupSendButton();
      this.setupDebugMode();
      this.setupValidationListener();
      this.syncWithCachedData(); // Adiciona sincronização com dados em cache

      // Atualiza estado inicial dos botões
      if (this.stepNavigationSystem) {
        this.updateNextButtonsState();
      }

      this.isInitialized = true;

      // Expõe método para debugging global se estiver em debug mode
      if (this.debugMode) {
        window.webflowButtonSystem = {
          forceUpdate: () => this.forceUpdateButtons(),
          checkState: () => this.updateNextButtonsState(),
          debugButtons: () => this.debugButtonStates(),
          instance: this,
        };
      }

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log('✅ Webflow Button System initialized');
      }
    } catch (error) {
      console.error('❌ Webflow Button System init failed:', error);
    }
  }

  waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve, { once: true });
      }
    });
  }

  setupNextButtons() {
    // Encontra todos os botões com element-function="next"
    const nextButtons = document.querySelectorAll('[element-function="next"]');

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log(`Found ${nextButtons.length} next buttons`);
    }

    nextButtons.forEach((button, index) => {
      this.setupNextButton(button, index);
    });

    // Também configura os botões prev do Webflow
    this.setupPrevButtons();
  }

  setupPrevButtons() {
    // Encontra todos os botões prev do Webflow
    const prevButtons = document.querySelectorAll('.step-btn.prev-btn');

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log(`Found ${prevButtons.length} prev buttons`);

      // Lista cada botão encontrado para debug
      prevButtons.forEach((button, index) => {
        // eslint-disable-next-line no-console
        console.log(`Prev button ${index}:`, button.outerHTML.substring(0, 100) + '...');
      });
    }

    prevButtons.forEach((button, index) => {
      this.setupPrevButton(button, index);
    });
  }

  setupPrevButton(button, index) {
    // Remove listeners existentes se houver
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);

    // Adiciona event listener para navegação anterior
    newButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Verifica se o botão está desabilitado
      if (newButton.disabled || newButton.classList.contains('button-disabled')) {
        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log(`Prev button ${index} is disabled, ignoring click`);
        }
        return;
      }

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log(`Prev button ${index} clicked`);
      }

      this.handlePrevNavigation(newButton);
    });

    // Adiciona indicador visual para debug
    if (this.debugMode) {
      newButton.setAttribute('data-webflow-button', 'prev');
      newButton.setAttribute('data-debug', 'true');
    }
  }

  async handlePrevNavigation(button) {
    try {
      // Usa o sistema de navegação existente se disponível
      if (
        this.stepNavigationSystem &&
        typeof this.stepNavigationSystem.previousStep === 'function'
      ) {
        this.stepNavigationSystem.previousStep();

        // CRITICAL FIX: Força atualização do estado dos botões após navegação
        // Aguarda um pequeno delay para garantir que a navegação seja processada
        setTimeout(() => {
          this.forceUpdateButtons();

          // EXTRA FIX: Força também uma revalidação através do sistema de navegação
          if (
            this.stepNavigationSystem &&
            typeof this.stepNavigationSystem.debouncedValidation === 'function'
          ) {
            this.stepNavigationSystem.debouncedValidation();
          }
        }, 100);
      } else {
        // Fallback: navegação manual para step anterior
        const currentSection = button.closest('.step-section');
        if (currentSection) {
          const currentStepAttr = currentSection.getAttribute('data-step');
          const currentStep = parseInt(currentStepAttr, 10);
          if (currentStep > 0) {
            this.manualNavigation(currentStep - 1);

            // Força atualização também no fallback
            setTimeout(() => {
              this.forceUpdateButtons();

              // EXTRA FIX: Força também uma revalidação através do sistema de navegação
              if (
                this.stepNavigationSystem &&
                typeof this.stepNavigationSystem.debouncedValidation === 'function'
              ) {
                this.stepNavigationSystem.debouncedValidation();
              }
            }, 100);
          }
        }
      }

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log('🔙 Prev navigation completed, forcing button update');
      }
    } catch (error) {
      console.error('Error in prev navigation:', error);
    }
  }

  setupNextButton(button, index) {
    // Remove listeners existentes se houver
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);

    // Adiciona event listener para navegação
    newButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Verifica se o botão está desabilitado
      if (newButton.disabled || newButton.classList.contains('button-disabled')) {
        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log(`Next button ${index} is disabled, ignoring click`);
        }
        return;
      }

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log(`Next button ${index} clicked`);
      }

      this.handleNextNavigation(newButton);
    });

    // Adiciona indicador visual para debug
    if (this.debugMode) {
      newButton.setAttribute('data-webflow-button', 'next');
      newButton.setAttribute('data-debug', 'true');
    }
  }

  async handleNextNavigation(button) {
    try {
      // Determina a seção atual baseada no botão
      const currentSection = button.closest('.step-section');
      if (!currentSection) {
        throw new Error('Cannot find current section');
      }

      const currentStepAttr = currentSection.getAttribute('data-step');
      const currentStep = parseInt(currentStepAttr, 10);

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log(`Current step: ${currentStep}`);
      }

      // Usa o sistema de navegação existente se disponível
      if (this.stepNavigationSystem && typeof this.stepNavigationSystem.nextStep === 'function') {
        // Verifica se pode prosseguir
        if (this.stepNavigationSystem.canProceedToNext()) {
          this.stepNavigationSystem.nextStep();

          // Força atualização do estado dos botões após navegação bem-sucedida
          setTimeout(() => {
            this.forceUpdateButtons();

            // EXTRA FIX: Força também uma revalidação através do sistema de navegação
            if (
              this.stepNavigationSystem &&
              typeof this.stepNavigationSystem.debouncedValidation === 'function'
            ) {
              this.stepNavigationSystem.debouncedValidation();
            }
          }, 100);
        } else {
          this.stepNavigationSystem.showValidationError();
        }
      } else {
        // Fallback: navegação manual
        this.manualNavigation(currentStep + 1);

        // Força atualização também no fallback
        setTimeout(() => {
          this.forceUpdateButtons();

          // EXTRA FIX: Força também uma revalidação através do sistema de navegação
          if (
            this.stepNavigationSystem &&
            typeof this.stepNavigationSystem.debouncedValidation === 'function'
          ) {
            this.stepNavigationSystem.debouncedValidation();
          }
        }, 100);
      }
    } catch (error) {
      console.error('Error in next navigation:', error);
      this.showValidationError('Erro ao navegar. Tente novamente.');
    }
  }

  manualNavigation(targetStep) {
    // Esconde todas as seções
    const allSections = document.querySelectorAll('.step-section');
    allSections.forEach((section) => {
      section.classList.remove('active');
      section.style.display = 'none';
    });

    // Mostra a seção alvo
    const targetSection = document.querySelector(`[data-step="${targetStep}"]`);
    if (targetSection) {
      targetSection.classList.add('active');
      targetSection.style.display = 'block';

      // Scroll para o topo da nova seção
      targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

      // Atualiza manualmente o currentStep se o sistema não estiver disponível
      if (this.stepNavigationSystem) {
        this.stepNavigationSystem.currentStep = targetStep;
      }

      // Força atualização dos botões após navegação manual
      setTimeout(() => {
        this.forceUpdateButtons();

        // EXTRA FIX: Força também uma revalidação através do sistema de navegação
        if (
          this.stepNavigationSystem &&
          typeof this.stepNavigationSystem.debouncedValidation === 'function'
        ) {
          this.stepNavigationSystem.debouncedValidation();
        }
      }, 100);

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log(`Manual navigation to step ${targetStep} completed`);
      }
    } else {
      console.warn(`Step ${targetStep} not found`);
    }
  }

  setupSendButton() {
    const sendButton = document.querySelector('[element-function="send"]');

    if (!sendButton) {
      console.warn('Send button not found');
      return;
    }

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log('Send button found, setting up...');
    }

    // Remove listeners existentes
    const newButton = sendButton.cloneNode(true);
    sendButton.parentNode.replaceChild(newButton, sendButton);

    // Adiciona event listener para envio
    newButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log('Send button clicked');
      }

      this.handleDataSubmission(newButton);
    });

    // Adiciona indicador visual para debug
    if (this.debugMode) {
      newButton.setAttribute('data-webflow-button', 'send');
      newButton.setAttribute('data-debug', 'true');
    }
  }

  async handleDataSubmission(button) {
    try {
      // Desabilita o botão durante o processamento
      const buttonText = button.querySelector('div');

      if (buttonText) {
        buttonText.textContent = 'Processando...';
      }
      button.disabled = true;

      // Coleta dados do formulário
      const formData = this.collectFormData();

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log('Form data collected:', JSON.stringify(formData, null, 2));
      }

      // Valida dados obrigatórios
      const validation = this.validateFormData(formData);
      if (!validation.isValid) {
        throw new Error(`Validação falhou: ${validation.errors.join(', ')}`);
      }

      // NEW: Check if Typebot integration is enabled and available
      if (this.useTypebot && this.typebotIntegration && this.typebotIntegration.isInitialized) {
        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log('🤖 Starting Typebot flow...');
        }

        // Update button text
        if (buttonText) {
          buttonText.textContent = 'Iniciando conversa...';
        }

        // Start Typebot flow instead of direct Supabase submission
        await this.startTypebotFlow(formData, button);
      } else {
        // Fallback: Direct Supabase submission (original behavior)
        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log('💾 Typebot not available, using direct Supabase submission...');
        }

        await this.handleDirectSubmission(formData, button);
      }
    } catch (error) {
      console.error('Error in data submission:', error);
      this.showValidationError(error.message || 'Erro ao processar dados. Tente novamente.');
    } finally {
      // Restaura o botão
      const buttonText = button.querySelector('div');
      if (buttonText) {
        buttonText.textContent = 'Enviar';
      }
      button.disabled = false;
    }
  }

  /**
   * NEW: Start Typebot flow with form data
   */
  async startTypebotFlow(formData, button) {
    try {
      // Store original button text
      const buttonText = button.querySelector('div');
      const originalText = buttonText ? buttonText.textContent : 'Enviar';

      // Update button to show loading
      if (buttonText) {
        buttonText.textContent = 'Abrindo conversa...';
      }

      // Prepare completion callback to handle Supabase submission after Typebot
      const onTypebotCompletion = async (enhancedFormData) => {
        // eslint-disable-next-line no-console
        console.log('🎯 [WebflowButton] onTypebotCompletion callback triggered');
        // eslint-disable-next-line no-console
        console.log(
          '📋 [WebflowButton] Enhanced form data received:',
          JSON.stringify(enhancedFormData, null, 2)
        );

        try {
          // eslint-disable-next-line no-console
          console.log('💾 [WebflowButton] Sending enhanced data to Supabase...');

          // Send enhanced data (with Typebot results) to Supabase
          const result = await this.sendToSupabase(enhancedFormData);

          // eslint-disable-next-line no-console
          console.log('📊 [WebflowButton] Supabase result:', JSON.stringify(result, null, 2));

          if (result.success) {
            // eslint-disable-next-line no-console
            console.log('✅ [WebflowButton] Data saved successfully to Supabase');

            // NEW: Send data to DGM Canvas using the dedicated integration module
            await dgmCanvasIntegration.sendData(enhancedFormData, result);

            // NEW: Navigate to results section (step 4) after successful completion
            this.navigateToResultsSection();

            this.showSuccessMessage('Conversa finalizada e dados salvos com sucesso!');
            this.handleSuccessfulSubmission(result);
          } else {
            console.error('❌ [WebflowButton] Supabase save failed:', result.error);
            throw new Error(result.error || 'Erro ao salvar dados após conversa');
          }

          // Reset button
          if (buttonText) {
            buttonText.textContent = originalText;
          }
        } catch (callbackError) {
          console.error('❌ [WebflowButton] Error in completion callback:', callbackError);
          this.showValidationError(
            'Erro ao salvar dados após a conversa: ' + callbackError.message
          );
        }
      };

      // Start Typebot flow
      await this.typebotIntegration.startTypebotFlow(formData, onTypebotCompletion);

      // Update UI to indicate Typebot is active
      if (buttonText) {
        buttonText.textContent = 'Conversa aberta ✓';
      }

      // Reset button text after popup opens
      setTimeout(() => {
        if (buttonText) {
          buttonText.textContent = originalText;
        }
      }, 2000);

      this.showSuccessMessage('Conversa iniciada! Complete o chat para finalizar.');
    } catch (error) {
      // If Typebot fails, fallback to direct submission
      console.error('🤖 Typebot flow failed, falling back to direct submission:', error);

      // Reset button
      const buttonText = button.querySelector('div');
      if (buttonText) {
        buttonText.textContent = 'Enviar';
      }

      await this.handleDirectSubmission(formData, button);
    }
  }

  /**
   * NEW: Handle direct Supabase submission (fallback method)
   */
  async handleDirectSubmission(formData, button) {
    try {
      const buttonText = button.querySelector('div');
      if (buttonText) {
        buttonText.textContent = 'Enviando...';
      }

      // Direct Supabase submission (original behavior)
      const result = await this.sendToSupabase(formData);

      if (result.success) {
        // NEW: Send data to DGM Canvas using the dedicated integration module
        await dgmCanvasIntegration.sendData(formData, result);

        this.showSuccessMessage('Dados enviados com sucesso!');
        this.handleSuccessfulSubmission(result);
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }
    } catch (error) {
      console.error('Direct submission error:', error);
      throw error; // Re-throw to be handled by main handleDataSubmission
    }
  }

  collectFormData() {
    const data = {
      timestamp: new Date().toISOString(),
      patrimonio: null,
      ativosEscolhidos: [],
      alocacao: {},
    };

    // Coleta valor do patrimônio
    const patrimonioInput = document.querySelector('[is-main="true"]');
    if (patrimonioInput) {
      data.patrimonio = this.parseCurrencyValue(patrimonioInput.value);
    }

    // Coleta ativos selecionados
    const selectedAssets = document.querySelectorAll('.selected-asset');
    selectedAssets.forEach((asset) => {
      const product = asset.getAttribute('ativo-product');
      const category = asset.getAttribute('ativo-category');
      if (product && category) {
        data.ativosEscolhidos.push({ product, category });
      }
    });

    // Coleta alocações
    const allocationItems = document.querySelectorAll('.patrimonio_interactive_item');
    allocationItems.forEach((item) => {
      const product = item.getAttribute('ativo-product');
      const category = item.getAttribute('ativo-category');
      const input = item.querySelector('.currency-input');
      const slider = item.querySelector('.slider');

      if (product && category && (input || slider)) {
        const value = input ? this.parseCurrencyValue(input.value) : 0;
        const percentage = slider ? parseFloat(slider.value) * 100 : 0;

        data.alocacao[`${category}-${product}`] = {
          value,
          percentage,
          category,
          product,
        };
      }
    });

    return data;
  }

  validateFormData(data) {
    const errors = [];

    // Valida patrimônio
    if (!data.patrimonio || data.patrimonio <= 0) {
      errors.push('Patrimônio deve ser maior que zero');
    }

    // Valida ativos escolhidos
    if (!data.ativosEscolhidos || data.ativosEscolhidos.length === 0) {
      errors.push('Pelo menos um ativo deve ser selecionado');
    }

    // Valida alocação
    const totalAlocado = Object.values(data.alocacao || {}).reduce(
      (sum, item) => sum + (item.value || 0),
      0
    );

    if (totalAlocado > data.patrimonio * 1.01) {
      // 1% de tolerância
      errors.push('Alocação total não pode exceder o patrimônio');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async sendToSupabase(data) {
    try {
      // eslint-disable-next-line no-console
      console.log(
        '💾 [WebflowButton] sendToSupabase called with data:',
        JSON.stringify(data, null, 2)
      );

      // Validar configuração do Supabase
      if (!validateSupabaseConfig()) {
        console.error('❌ [WebflowButton] Supabase configuration invalid');
        return {
          success: false,
          error: 'Supabase não configurado. Verifique as credenciais.',
        };
      }

      // eslint-disable-next-line no-console
      console.log('✅ [WebflowButton] Supabase configuration valid');

      // Preparar dados para o banco
      const totalAlocado = Object.values(data.alocacao || {}).reduce(
        (sum, item) => sum + (item.value || 0),
        0
      );

      // eslint-disable-next-line no-console
      console.log('🧮 [WebflowButton] Total alocado calculated:', totalAlocado);

      const submissionData = {
        patrimonio: data.patrimonio,
        ativos_escolhidos: data.ativosEscolhidos,
        alocacao: data.alocacao,
        submitted_at: data.timestamp,
        user_agent:
          typeof window !== 'undefined' && window.navigator
            ? window.navigator.userAgent
            : 'Unknown',
        session_id: this.generateSessionId(),
        total_alocado: totalAlocado,
        percentual_alocado: data.patrimonio > 0 ? (totalAlocado / data.patrimonio) * 100 : 0,
        patrimonio_restante: data.patrimonio - totalAlocado,
        // Informações do usuário coletadas via Typebot
        nome: data.nome || null,
        email: data.email || null,
        // Informações adicionais do Typebot (se disponíveis)
        typebot_session_id: data.typebotSessionId || null,
        typebot_result_id: data.typebotResultId || null,
      };

      // eslint-disable-next-line no-console
      console.log(
        '📋 [WebflowButton] Submission data prepared:',
        JSON.stringify(submissionData, null, 2)
      );

      // eslint-disable-next-line no-console
      console.log('🚀 [WebflowButton] Inserting into Supabase table:', TABLE_NAME);

      // Inserir no Supabase e retornar dados inseridos
      const { data: result, error } = await supabase
        .from(TABLE_NAME)
        .insert([submissionData])
        .select();

      // eslint-disable-next-line no-console
      console.log(
        '📊 [WebflowButton] Supabase insert result:',
        JSON.stringify({ result, error }, null, 2)
      );

      if (error) {
        console.error('❌ [WebflowButton] Supabase insert error:', error);
        throw error;
      }

      // eslint-disable-next-line no-console
      console.log('✅ [WebflowButton] Data successfully inserted into Supabase');

      return {
        success: true,
        id: result?.[0]?.id || 'inserted_successfully',
        data: result?.[0] || submissionData,
        message: 'Dados enviados com sucesso para o banco!',
      };
    } catch (error) {
      console.error('Supabase submission error:', error);
      return {
        success: false,
        error: error.message || 'Erro ao enviar dados para o banco',
      };
    }
  }

  handleSuccessfulSubmission(result) {
    // Mostra página de resultado ou redireciona
    // eslint-disable-next-line no-console
    console.log('Submission successful:', JSON.stringify(result, null, 2));

    // TODO: Implementar navegação para página de resultado
    // ou mostrar modal de sucesso
  }

  parseCurrencyValue(value) {
    if (!value || typeof value !== 'string') return 0;
    return parseFloat(value.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
  }

  showValidationError(message) {
    // Cria ou atualiza toast de erro
    this.showToast(message, 'error');
  }

  showSuccessMessage(message) {
    // Cria ou atualiza toast de sucesso
    this.showToast(message, 'success');
  }

  showToast(message, type = 'info') {
    // Remove toasts existentes
    const existingToasts = document.querySelectorAll('.webflow-toast');
    existingToasts.forEach((toast) => toast.remove());

    // Cria novo toast
    const toast = document.createElement('div');
    toast.className = `webflow-toast webflow-toast--${type}`;
    toast.textContent = message;

    // Aplica estilos
    Object.assign(toast.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '16px 24px',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '600',
      zIndex: '10001',
      animation: 'slideInRight 0.3s ease',
      maxWidth: '400px',
      wordWrap: 'break-word',
    });

    // Cores baseadas no tipo
    const colors = {
      info: '#4a90e2',
      success: '#926f1b',
      error: '#f44336',
    };
    toast.style.background = colors[type] || colors.info;

    document.body.appendChild(toast);

    // Remove após 5 segundos
    setTimeout(() => {
      if (toast.parentNode) {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => toast.remove(), 300);
      }
    }, 5000);
  }

  setupDebugMode() {
    // Ativa modo debug se estiver em desenvolvimento
    this.debugMode =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      window.location.search.includes('debug=true');

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log('🔧 Webflow Button System - Debug Mode Active');

      // Adiciona indicador visual
      const debugIndicator = document.createElement('div');
      debugIndicator.innerHTML = '🔧 DEBUG MODE';
      debugIndicator.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: #ff9800;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        z-index: 10000;
      `;
      document.body.appendChild(debugIndicator);
    }
  }

  // Método para integração futura com Supabase
  setSupabaseClient(client) {
    this.supabaseClient = client;
    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log('Supabase client configured');
    }
  }

  // Métodos de utilidade
  generateSessionId() {
    // Gera um ID único para a sessão
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  setupValidationListener() {
    // Escuta mudanças no estado de validação do step navigation
    document.addEventListener('stepValidationChanged', (event) => {
      this.updateNextButtonsState();

      if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log('Step validation changed:', event.detail);
      }
    });

    // Faz verificações iniciais com intervalos para capturar dados restaurados
    let attempts = 0;
    const maxAttempts = 10; // 5 segundos total
    const checkInterval = 500;

    const periodicCheck = () => {
      attempts += 1;
      this.updateNextButtonsState();

      if (this.debugMode && attempts === 1) {
        // eslint-disable-next-line no-console
        console.log('Starting periodic validation checks for cached data...');
      }

      if (attempts < maxAttempts) {
        setTimeout(periodicCheck, checkInterval);
      } else if (this.debugMode) {
        // eslint-disable-next-line no-console
        console.log('Periodic validation checks completed');
      }
    };

    // Inicia as verificações após um pequeno delay
    setTimeout(periodicCheck, 100);
  }

  // Método específico para sincronização após carregamento de dados em cache
  syncWithCachedData() {
    // Adiciona listener para quando o documento estiver completamente carregado
    if (document.readyState === 'complete') {
      this.performDelayedSync();
    } else {
      window.addEventListener('load', () => this.performDelayedSync(), { once: true });
    }
  }

  performDelayedSync() {
    // Executa várias verificações com intervalos crescentes para capturar dados restaurados
    const delays = [100, 300, 600, 1000, 2000]; // Intervalos em ms

    delays.forEach((delay) => {
      setTimeout(() => {
        this.updateNextButtonsState();

        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log(`Delayed sync check at ${delay}ms`);
        }
      }, delay);
    });
  }

  updateNextButtonsState() {
    // Atualiza estado dos botões next baseado na validação
    const nextButtons = document.querySelectorAll('[element-function="next"]');

    nextButtons.forEach((button) => {
      if (
        this.stepNavigationSystem &&
        typeof this.stepNavigationSystem.canProceedToNext === 'function'
      ) {
        const canProceed = this.stepNavigationSystem.canProceedToNext();

        // Desabilita/habilita o botão funcionalmente
        button.disabled = !canProceed;

        // Adiciona ou remove apenas a classe button-disabled
        if (canProceed) {
          button.classList.remove('button-disabled');
        } else {
          button.classList.add('button-disabled');
        }

        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log(
            `Button ${button.getAttribute('element-function')} - canProceed: ${canProceed}`
          );
        }
      } else {
        // Se o sistema ainda não estiver disponível, mantém botões desabilitados por segurança
        button.disabled = true;
        button.classList.add('button-disabled');

        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log('StepNavigationSystem not available yet, keeping buttons disabled');
        }
      }
    });

    // Atualiza estado dos botões prev do Webflow
    this.updatePrevButtonsState();
  }

  updatePrevButtonsState() {
    // Atualiza estado dos botões prev baseado no step atual
    const prevButtons = document.querySelectorAll('.step-btn.prev-btn');

    prevButtons.forEach((button) => {
      if (this.stepNavigationSystem) {
        const isFirstStep = this.stepNavigationSystem.currentStep === 0;

        // Desabilita/habilita o botão funcionalmente
        button.disabled = isFirstStep;

        // Adiciona ou remove a classe button-disabled
        if (isFirstStep) {
          button.classList.add('button-disabled');
        } else {
          button.classList.remove('button-disabled');
        }

        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log(
            `Prev button - currentStep: ${this.stepNavigationSystem.currentStep}, disabled: ${isFirstStep}`
          );
        }
      } else {
        // Se o sistema ainda não estiver disponível, mantém botões desabilitados por segurança
        button.disabled = true;
        button.classList.add('button-disabled');

        if (this.debugMode) {
          // eslint-disable-next-line no-console
          console.log('StepNavigationSystem not available yet, keeping prev buttons disabled');
        }
      }
    });
  }

  // Método público para forçar atualização dos botões (útil para debugging e integrações)
  forceUpdateButtons() {
    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log('🔄 Forcing button state update...');

      if (this.stepNavigationSystem) {
        // eslint-disable-next-line no-console
        console.log(`📍 Current step: ${this.stepNavigationSystem.currentStep}`);
        // eslint-disable-next-line no-console
        console.log(`✅ Can proceed: ${this.stepNavigationSystem.canProceedToNext()}`);
      } else {
        // eslint-disable-next-line no-console
        console.log('⚠️ StepNavigationSystem not available');
      }
    }

    // Força uma nova verificação de validação se disponível
    if (
      this.stepNavigationSystem &&
      typeof this.stepNavigationSystem.updateNavigationState === 'function'
    ) {
      this.stepNavigationSystem.updateNavigationState();
    }

    this.updateNextButtonsState();

    // Também dispara um evento personalizado para outros sistemas
    document.dispatchEvent(
      new CustomEvent('webflowButtonsUpdated', {
        detail: {
          timestamp: Date.now(),
          systemInitialized: this.isInitialized,
          currentStep: this.stepNavigationSystem?.currentStep || -1,
          canProceed: this.stepNavigationSystem?.canProceedToNext() || false,
        },
      })
    );

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log('✅ Button state update completed');
    }
  }

  /**
   * NEW: Set Typebot integration system
   * @param {TypebotIntegrationSystem} typebotIntegration - The typebot integration instance
   */
  setTypebotIntegration(typebotIntegration) {
    this.typebotIntegration = typebotIntegration;

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log('🤖 Typebot integration set:', !!typebotIntegration);
    }
  }

  /**
   * NEW: Enable or disable Typebot integration
   * @param {boolean} enabled - Whether to use Typebot
   */
  setTypebotEnabled(enabled) {
    this.useTypebot = enabled;

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log(`🤖 Typebot integration ${enabled ? 'enabled' : 'disabled'}`);
    }
  }

  /**
   * NEW: Get Typebot integration status
   * @returns {Object} Status information
   */
  getTypebotStatus() {
    return {
      enabled: this.useTypebot,
      available: !!this.typebotIntegration,
      initialized: this.typebotIntegration?.isInitialized || false,
      active: this.typebotIntegration?.isTypebotActive || false,
    };
  }

  /**
   * NEW: Configure DGM Canvas integration
   * @param {Object} config - Configuration options for DGM Canvas
   */
  async configureDGMCanvas(config = {}) {
    // Force reinitialize to ensure new configuration is applied
    await dgmCanvasIntegration.reinitialize(config);

    if (this.debugMode) {
      // eslint-disable-next-line no-console
      console.log('🎨 [WebflowButton] DGM Canvas integration configured:', config);
      // eslint-disable-next-line no-console
      console.log('🎯 [WebflowButton] DGM Canvas status:', dgmCanvasIntegration.getStatus());
    }
  }

  /**
   * NEW: Get DGM Canvas integration status
   * @returns {Object} Status information
   */
  getDGMCanvasStatus() {
    return dgmCanvasIntegration.getStatus();
  }

  /**
   * Navigate to the results section (step 4) after Typebot completion
   */
  navigateToResultsSection() {
    try {
      if (this.stepNavigationSystem && typeof this.stepNavigationSystem.showStep === 'function') {
        // eslint-disable-next-line no-console
        console.log('🎯 [WebflowButton] Navigating to results section (step 4)...');

        // Navigate to step 4 (results section)
        this.stepNavigationSystem.showStep(4);

        // eslint-disable-next-line no-console
        console.log('✅ [WebflowButton] Successfully navigated to results section');
      } else {
        console.warn(
          '⚠️ [WebflowButton] Step navigation system not available for results navigation'
        );
      }
    } catch (error) {
      console.error('❌ [WebflowButton] Error navigating to results section:', error);
    }
  }

  /**
   * DEBUG: Method to check button states and validation
   * Useful for troubleshooting button state issues
   */
  debugButtonStates() {
    // eslint-disable-next-line no-console
    console.log('🔍 === WEBFLOW BUTTON DEBUG ===');

    // Check StepNavigationSystem status
    if (this.stepNavigationSystem) {
      // eslint-disable-next-line no-console
      console.log('📍 StepNavigationSystem status:');
      // eslint-disable-next-line no-console
      console.log(`  Current step: ${this.stepNavigationSystem.currentStep}`);
      // eslint-disable-next-line no-console
      console.log(`  Can proceed: ${this.stepNavigationSystem.canProceedToNext()}`);
    } else {
      // eslint-disable-next-line no-console
      console.log('❌ StepNavigationSystem not available');
    }

    // Check Next buttons
    const nextButtons = document.querySelectorAll('[element-function="next"]');
    // eslint-disable-next-line no-console
    console.log(`🔘 Next buttons found: ${nextButtons.length}`);
    nextButtons.forEach((button, index) => {
      // eslint-disable-next-line no-console
      console.log(`  Next ${index}: disabled=${button.disabled}, classes=${button.className}`);
    });

    // Check Prev buttons
    const prevButtons = document.querySelectorAll('.step-btn.prev-btn');
    // eslint-disable-next-line no-console
    console.log(`⬅️ Prev buttons found: ${prevButtons.length}`);
    prevButtons.forEach((button, index) => {
      // eslint-disable-next-line no-console
      console.log(`  Prev ${index}: disabled=${button.disabled}, classes=${button.className}`);
    });

    // Check validation for current step
    if (this.stepNavigationSystem) {
      const currentStepName =
        this.stepNavigationSystem.steps?.[this.stepNavigationSystem.currentStep]?.name;
      // eslint-disable-next-line no-console
      console.log(`📋 Current step validation (${currentStepName}):`);

      if (currentStepName === 'money') {
        const mainInput = document.querySelector('[is-main="true"]');
        const value = mainInput ? this.parseCurrencyValue(mainInput.value) : 0;
        // eslint-disable-next-line no-console
        console.log(`  Money input value: ${value} (valid: ${value > 0})`);
      } else if (currentStepName === 'assets') {
        const selectedAssets = document.querySelectorAll('.selected-asset');
        // eslint-disable-next-line no-console
        console.log(`  Selected assets: ${selectedAssets.length}`);
      }
    }

    // eslint-disable-next-line no-console
    console.log('🔍 === END DEBUG ===');
  }
}
